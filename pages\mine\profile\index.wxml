<view class="container profile-edit">
  <diy-navbar :isFixed="true" bgColor="white" CustomBar='60'>
    <view slot="content">
      <view class="flex align-center flex-nowrap justify-start diygw-col-24 title-left">
        编辑个人信息
      </view>
    </view>
  </diy-navbar>

  <view class="profile-form">
    <!-- 头像编辑 -->
    <view class="form-section">
      <view class="section-title">头像</view>
      <view class="avatar-section">
        <view class="avatar-container" bind:tap="uploadAvatar">
          <image
            src="{{formData.avatar || '//xian7.zos.ctyun.cn/pet/static/memberAvatar.png'}}"
            class="avatar-image"
            mode="aspectFill"
          />
          <view class="avatar-overlay {{uploading ? 'uploading' : ''}}">
            <text class="avatar-text">{{uploading ? '上传中...' : '点击更换'}}</text>
          </view>
        </view>
        <view class="avatar-tips">
          <text class="tip-text">支持JPG、PNG格式，建议尺寸200x200像素</text>
        </view>
      </view>
    </view>

    <!-- 姓名编辑 -->
    <view class="form-section">
      <view class="section-title">姓名</view>
      <view class="input-container">
        <input 
          class="form-input" 
          placeholder="请输入您的姓名" 
          value="{{formData.name}}"
          data-field="name"
          bindinput="onInputChange"
          maxlength="20"
        />
      </view>
    </view>

    <!-- 手机号显示（不可编辑） -->
    <view class="form-section">
      <view class="section-title">手机号</view>
      <view class="readonly-field">
        <text class="readonly-text">{{userInfo.phone || '未绑定'}}</text>
        <text class="readonly-note">（不可修改）</text>
      </view>
    </view>

    <!-- 职位显示（不可编辑） -->
    <view wx:if="{{userInfo.positionName}}" class="form-section">
      <view class="section-title">职位</view>
      <view class="readonly-field">
        <text class="readonly-text">{{userInfo.positionName}}</text>
        <text class="readonly-note">（不可修改）</text>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-buttons">
    <button 
      class="btn btn-cancel" 
      bind:tap="cancelEdit"
      disabled="{{saving}}"
    >
      取消
    </button>
    <button 
      class="btn btn-save {{saving ? 'loading' : ''}}" 
      bind:tap="saveProfile"
      disabled="{{saving || uploading}}"
    >
      {{saving ? '保存中...' : '保存'}}
    </button>
  </view>
</view>
