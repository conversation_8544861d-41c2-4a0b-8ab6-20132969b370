// pages/mine/profile/index.js
import userApi from '../../../api/modules/user.js';
import Session from '../../../common/Session.js';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    userInfo: {},
    formData: {
      name: '',
      avatar: ''
    },
    originalFormData: {
      name: '',
      avatar: ''
    },
    uploading: false,
    saving: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    this.initUserInfo();
  },

  // 初始化用户信息
  initUserInfo() {
    const userInfo = Session.getUser();
    if (!userInfo || !userInfo.id) {
      wx.redirectTo({
        url: '/pages/login/index',
      });
      return;
    }

    const formData = {
      name: userInfo.name || '',
      avatar: userInfo.avatar || ''
    };

    this.setData({
      userInfo,
      formData,
      originalFormData: { ...formData }
    });
  },

  // 输入框变化处理
  onInputChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 上传头像
  uploadAvatar() {
    if (this.data.uploading) {
      return;
    }

    this.setData({ uploading: true });

    const keyPrefix = `avatar/${this.data.userInfo.id}/`;

    this.uploadImage(
      this,
      '', // 存储字段
      keyPrefix, // 上传key前缀
      1 // 最大数量
    ).then(res => {
      if (res && res.length > 0) {
        this.setData({
          'formData.avatar': res[0],
          uploading: false
        });

        wx.showToast({
          title: '头像上传成功',
          icon: 'success'
        });
      } else {
        this.setData({ uploading: false });
      }
    }).catch(error => {
      console.error('头像上传失败:', error);
      this.setData({ uploading: false });
      wx.showToast({
        title: '头像上传失败',
        icon: 'none'
      });
    });
  },

  // 保存信息
  async saveProfile() {
    if (this.data.saving) {
      return;
    }

    const { formData, originalFormData, userInfo } = this.data;

    // 检查是否有修改
    if (formData.name === originalFormData.name && formData.avatar === originalFormData.avatar) {
      wx.showToast({
        title: '没有修改内容',
        icon: 'none'
      });
      return;
    }

    // 验证姓名
    if (!formData.name || formData.name.trim() === '') {
      wx.showToast({
        title: '请输入姓名',
        icon: 'none'
      });
      return;
    }

    this.setData({ saving: true });

    try {
      // 构建提交数据
      const submitData = {
        name: formData.name.trim(),
        avatar: formData.avatar
      };

      console.log('提交员工信息修改:', submitData);

      // 调用员工信息修改接口
      const result = await userApi.updateEmployeeProfile(submitData);

      if (result) {
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        });

        // 更新本地用户信息
        const newUserInfo = {
          ...userInfo,
          name: submitData.name,
          avatar: submitData.avatar
        };
        Session.setUser(newUserInfo);

        // 更新原始数据
        this.setData({
          userInfo: newUserInfo,
          originalFormData: { ...formData },
          saving: false
        });

        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        throw new Error('保存失败');
      }
    } catch (error) {
      console.error('保存个人信息失败:', error);
      this.setData({ saving: false });
      wx.showToast({
        title: '保存失败，请重试',
        icon: 'none'
      });
    }
  },

  // 取消编辑
  cancelEdit() {
    const { originalFormData } = this.data;
    
    // 检查是否有修改
    const { formData } = this.data;
    if (formData.name !== originalFormData.name || formData.avatar !== originalFormData.avatar) {
      wx.showModal({
        title: '提示',
        content: '您有未保存的修改，确定要离开吗？',
        success: (res) => {
          if (res.confirm) {
            wx.navigateBack();
          }
        }
      });
    } else {
      wx.navigateBack();
    }
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {}
});
