/* pages/mine/profile/index.wxss */
.profile-edit {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.profile-form {
  padding: 40rpx 30rpx;
}

.form-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
}

/* 头像编辑样式 */
.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.avatar-container {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 20rpx;
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s;
}

.avatar-container:active .avatar-overlay {
  opacity: 1;
}

.avatar-overlay.uploading {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.7);
}

.avatar-text {
  color: #fff;
  font-size: 24rpx;
  text-align: center;
}

.avatar-tips {
  text-align: center;
}

.tip-text {
  font-size: 24rpx;
  color: #999;
}

/* 输入框样式 */
.input-container {
  position: relative;
}

.form-input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  font-size: 30rpx;
  color: #333;
  background-color: #fff;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #4A90E2;
  box-shadow: 0 0 0 4rpx rgba(74, 144, 226, 0.1);
}

.form-input::placeholder {
  color: #999;
}

/* 只读字段样式 */
.readonly-field {
  display: flex;
  align-items: center;
  height: 80rpx;
  padding: 0 20rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  border: 2rpx solid #f0f0f0;
}

.readonly-text {
  font-size: 30rpx;
  color: #666;
  flex: 1;
}

.readonly-note {
  font-size: 24rpx;
  color: #999;
}

/* 操作按钮样式 */
.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  padding: 30rpx;
  background-color: #fff;
  border-top: 1rpx solid #e5e5e5;
  gap: 20rpx;
}

.btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-cancel {
  background-color: #f5f5f5;
  color: #666;
}

.btn-cancel:active {
  background-color: #e5e5e5;
}

.btn-save {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.3);
}

.btn-save:active {
  background: linear-gradient(135deg, #357ABD 0%, #2E6BA8 100%);
  transform: translateY(2rpx);
}

.btn-save.loading {
  background-color: #ccc;
  color: #999;
}

.btn:disabled {
  background-color: #f0f0f0;
  color: #ccc;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .profile-form {
    padding: 30rpx 20rpx;
  }
  
  .form-section {
    padding: 30rpx 20rpx;
    margin-bottom: 20rpx;
  }
  
  .avatar-container {
    width: 140rpx;
    height: 140rpx;
  }
}
